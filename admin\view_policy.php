<?php
session_start();

// Check if user is logged in and is admin
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Administrator") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$policy_id = isset($_GET['id']) ? $_GET['id'] : null;

if(!$policy_id) {
    header("location: policies.php");
    exit;
}

// Get policy details
$query = "SELECT p.*, pt.type_name, pt.description as type_description,
                 CONCAT(u.first_name, ' ', u.last_name) as created_by_name,
                 COUNT(cp.customer_policy_id) as customer_count
          FROM policies p
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          LEFT JOIN users u ON p.created_by = u.user_id
          LEFT JOIN customer_policies cp ON p.policy_id = cp.policy_id
          WHERE p.policy_id = :policy_id
          GROUP BY p.policy_id";

$stmt = $conn->prepare($query);
$stmt->bindParam(':policy_id', $policy_id);
$stmt->execute();
$policy = $stmt->fetch(PDO::FETCH_ASSOC);

if(!$policy) {
    header("location: policies.php");
    exit;
}

// Get customers with this policy
$customers_query = "SELECT cp.*, CONCAT(u.first_name, ' ', u.last_name) as customer_name, u.email
                    FROM customer_policies cp
                    JOIN users u ON cp.customer_id = u.user_id
                    WHERE cp.policy_id = :policy_id
                    ORDER BY cp.start_date DESC";
$customers_stmt = $conn->prepare($customers_query);
$customers_stmt->bindParam(':policy_id', $policy_id);
$customers_stmt->execute();
$customers = $customers_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "View Policy: " . $policy['policy_name'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .admin-header { background: #343a40; color: white; padding: 15px 0; margin-bottom: 30px; }
        .admin-nav { display: flex; justify-content: space-between; align-items: center; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin-right: 10px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-secondary { background: #6c757d; color: white; }
        .policy-details { background: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); margin-bottom: 30px; }
        .detail-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 20px; }
        .table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background: #f8f9fa; font-weight: 600; }
        .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-discontinued { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="admin-container">
            <div class="admin-nav">
                <h2>Zamara Admin - Policy Details</h2>
                <div>
                    <span>Welcome, <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?></span>
                    <a href="dashboard.php" class="btn btn-secondary">Dashboard</a>
                    <a href="../logout.php" class="btn btn-danger">Logout</a>
                </div>
            </div>
        </div>
    </header>

    <div class="admin-container">
        <div style="margin-bottom: 20px;">
            <a href="policies.php" class="btn btn-secondary">← Back to Policies</a>
            <a href="edit_policy.php?id=<?php echo $policy['policy_id']; ?>" class="btn btn-warning">Edit Policy</a>
        </div>

        <div class="policy-details">
            <h1><?php echo htmlspecialchars($policy['policy_name']); ?></h1>
            <span class="status-badge status-<?php echo strtolower($policy['status']); ?>">
                <?php echo $policy['status']; ?>
            </span>

            <div class="detail-grid">
                <div>
                    <h3>Policy Information</h3>
                    <p><strong>Policy ID:</strong> <?php echo $policy['policy_id']; ?></p>
                    <p><strong>Policy Type:</strong> <?php echo htmlspecialchars($policy['type_name']); ?></p>
                    <p><strong>Premium Amount:</strong> KSH <?php echo number_format($policy['premium_amount'], 2); ?></p>
                    <p><strong>Coverage Amount:</strong> KSH <?php echo number_format($policy['coverage_amount'], 2); ?></p>
                    <p><strong>Duration:</strong> <?php echo htmlspecialchars($policy['duration'] ?: 'Not specified'); ?></p>
                    <p><strong>Status:</strong> <?php echo $policy['status']; ?></p>
                </div>
                
                <div>
                    <h3>Additional Details</h3>
                    <p><strong>Created By:</strong> <?php echo htmlspecialchars($policy['created_by_name'] ?: 'Unknown'); ?></p>
                    <p><strong>Created Date:</strong> <?php echo date('M d, Y H:i', strtotime($policy['created_at'])); ?></p>
                    <p><strong>Last Updated:</strong> <?php echo $policy['updated_at'] ? date('M d, Y H:i', strtotime($policy['updated_at'])) : 'Never'; ?></p>
                    <p><strong>Active Customers:</strong> <?php echo $policy['customer_count']; ?></p>
                </div>
            </div>

            <?php if($policy['description']): ?>
                <div style="margin-top: 30px;">
                    <h3>Description</h3>
                    <p><?php echo nl2br(htmlspecialchars($policy['description'])); ?></p>
                </div>
            <?php endif; ?>

            <?php if($policy['terms_conditions']): ?>
                <div style="margin-top: 30px;">
                    <h3>Terms & Conditions</h3>
                    <p><?php echo nl2br(htmlspecialchars($policy['terms_conditions'])); ?></p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Customers with this policy -->
        <div style="background: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
            <h2>Customers with this Policy (<?php echo count($customers); ?>)</h2>
            
            <?php if(count($customers) > 0): ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Customer Name</th>
                            <th>Email</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Status</th>
                            <th>Premium Paid</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($customers as $customer): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($customer['customer_name']); ?></td>
                            <td><?php echo htmlspecialchars($customer['email']); ?></td>
                            <td><?php echo date('M d, Y', strtotime($customer['start_date'])); ?></td>
                            <td><?php echo $customer['end_date'] ? date('M d, Y', strtotime($customer['end_date'])) : 'Ongoing'; ?></td>
                            <td>
                                <span class="status-badge status-<?php echo strtolower($customer['status']); ?>">
                                    <?php echo $customer['status']; ?>
                                </span>
                            </td>
                            <td>KSH <?php echo number_format($customer['premium_paid'], 2); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p style="text-align: center; color: #6c757d; padding: 40px 0;">No customers have subscribed to this policy yet.</p>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
