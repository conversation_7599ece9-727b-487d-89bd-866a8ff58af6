<?php
session_start();

// Check if user is logged in and is admin
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["primary_role"] !== "Administrator") {
    header("location: ../login.php");
    exit;
}

require_once '../includes/db_config.php';

$policy_id = isset($_GET['id']) ? $_GET['id'] : null;
$success_message = "";
$error_message = "";

if(!$policy_id) {
    header("location: policies.php");
    exit;
}

// Handle form submission
if($_SERVER["REQUEST_METHOD"] == "POST") {
    $policy_name = trim($_POST['policy_name']);
    $policy_type_id = $_POST['policy_type_id'];
    $description = trim($_POST['description']);
    $premium_amount = $_POST['premium_amount'];
    $coverage_amount = $_POST['coverage_amount'];
    $duration = trim($_POST['duration']);
    $terms_conditions = trim($_POST['terms_conditions']);
    $status = $_POST['status'];
    
    try {
        $update_sql = "UPDATE policies SET 
                       policy_name = :policy_name,
                       policy_type_id = :policy_type_id,
                       description = :description,
                       premium_amount = :premium_amount,
                       coverage_amount = :coverage_amount,
                       duration = :duration,
                       terms_conditions = :terms_conditions,
                       status = :status,
                       updated_at = NOW()
                       WHERE policy_id = :policy_id";
        
        $stmt = $conn->prepare($update_sql);
        $stmt->bindParam(':policy_name', $policy_name);
        $stmt->bindParam(':policy_type_id', $policy_type_id);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':premium_amount', $premium_amount);
        $stmt->bindParam(':coverage_amount', $coverage_amount);
        $stmt->bindParam(':duration', $duration);
        $stmt->bindParam(':terms_conditions', $terms_conditions);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':policy_id', $policy_id);
        
        if($stmt->execute()) {
            $success_message = "Policy updated successfully!";
        } else {
            $error_message = "Error updating policy.";
        }
    } catch(PDOException $e) {
        $error_message = "Error updating policy: " . $e->getMessage();
    }
}

// Get policy details
$query = "SELECT p.*, pt.type_name
          FROM policies p
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          WHERE p.policy_id = :policy_id";

$stmt = $conn->prepare($query);
$stmt->bindParam(':policy_id', $policy_id);
$stmt->execute();
$policy = $stmt->fetch(PDO::FETCH_ASSOC);

if(!$policy) {
    header("location: policies.php");
    exit;
}

// Get policy types for dropdown
$types_query = "SELECT * FROM policy_types ORDER BY type_name";
$policy_types = $conn->query($types_query)->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Edit Policy: " . $policy['policy_name'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .admin-header { background: #343a40; color: white; padding: 15px 0; margin-bottom: 30px; }
        .admin-nav { display: flex; justify-content: space-between; align-items: center; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin-right: 10px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .form-container { background: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); }
        .form-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .form-group { margin-bottom: 20px; }
        .form-control { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
        .form-control:focus { border-color: #007bff; outline: none; }
        label { display: block; margin-bottom: 5px; font-weight: 600; color: #333; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="admin-container">
            <div class="admin-nav">
                <h2>Zamara Admin - Edit Policy</h2>
                <div>
                    <span>Welcome, <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?></span>
                    <a href="dashboard.php" class="btn btn-secondary">Dashboard</a>
                    <a href="../logout.php" class="btn btn-danger">Logout</a>
                </div>
            </div>
        </div>
    </header>

    <div class="admin-container">
        <div style="margin-bottom: 20px;">
            <a href="policies.php" class="btn btn-secondary">← Back to Policies</a>
            <a href="view_policy.php?id=<?php echo $policy['policy_id']; ?>" class="btn btn-primary">View Policy</a>
        </div>

        <?php if($success_message): ?>
            <div class="alert alert-success"><?php echo $success_message; ?></div>
        <?php endif; ?>

        <?php if($error_message): ?>
            <div class="alert alert-danger"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <div class="form-container">
            <h1>Edit Policy: <?php echo htmlspecialchars($policy['policy_name']); ?></h1>
            
            <form method="post" style="margin-top: 30px;">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="policy_name">Policy Name *</label>
                        <input type="text" id="policy_name" name="policy_name" class="form-control" 
                               value="<?php echo htmlspecialchars($policy['policy_name']); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="policy_type_id">Policy Type *</label>
                        <select id="policy_type_id" name="policy_type_id" class="form-control" required>
                            <?php foreach($policy_types as $type): ?>
                                <option value="<?php echo $type['policy_type_id']; ?>" 
                                        <?php echo ($type['policy_type_id'] == $policy['policy_type_id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($type['type_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="premium_amount">Premium Amount (KSH) *</label>
                        <input type="number" id="premium_amount" name="premium_amount" class="form-control" 
                               step="0.01" min="0" value="<?php echo $policy['premium_amount']; ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="coverage_amount">Coverage Amount (KSH) *</label>
                        <input type="number" id="coverage_amount" name="coverage_amount" class="form-control" 
                               step="0.01" min="0" value="<?php echo $policy['coverage_amount']; ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="duration">Duration</label>
                        <input type="text" id="duration" name="duration" class="form-control" 
                               value="<?php echo htmlspecialchars($policy['duration']); ?>" 
                               placeholder="e.g., 12 months, 1 year, Lifetime">
                    </div>

                    <div class="form-group">
                        <label for="status">Status *</label>
                        <select id="status" name="status" class="form-control" required>
                            <option value="Active" <?php echo ($policy['status'] == 'Active') ? 'selected' : ''; ?>>Active</option>
                            <option value="Inactive" <?php echo ($policy['status'] == 'Inactive') ? 'selected' : ''; ?>>Inactive</option>
                            <option value="Discontinued" <?php echo ($policy['status'] == 'Discontinued') ? 'selected' : ''; ?>>Discontinued</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea id="description" name="description" class="form-control" rows="4" 
                              placeholder="Describe the policy coverage and benefits"><?php echo htmlspecialchars($policy['description']); ?></textarea>
                </div>

                <div class="form-group">
                    <label for="terms_conditions">Terms & Conditions</label>
                    <textarea id="terms_conditions" name="terms_conditions" class="form-control" rows="6" 
                              placeholder="Enter the terms and conditions for this policy"><?php echo htmlspecialchars($policy['terms_conditions']); ?></textarea>
                </div>

                <div style="margin-top: 30px; text-align: right;">
                    <a href="view_policy.php?id=<?php echo $policy['policy_id']; ?>" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-success">Update Policy</button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
