<?php
session_start();

if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: login.php");
    exit;
}

// Check if the user has customer role
$is_customer = false;
foreach($_SESSION["roles"] as $role) {
    if($role['role_name'] == "Customer") {
        $is_customer = true;
        break;
    }
}

// If not customer, redirect to appropriate dashboard
if(!$is_customer) {
    switch($_SESSION["primary_role"]) {
        case "Administrator":
            header("location: admin/dashboard.php");
            break;
        case "Insurance Agent":
            header("location: agent/dashboard.php");
            break;
        case "System Accountant":
            header("location: accountant/dashboard.php");
            break;
    }
    exit;
}

$page_title = "Dashboard";
$page_description = "Manage your Zamara insurance and financial services.";

require_once 'includes/db_config.php';


$query = "SELECT COUNT(*) as active_policies FROM customer_policies WHERE customer_id = :customer_id AND status = 'Active'";
$stmt = $conn->prepare($query);
$stmt->bindParam(":customer_id", $_SESSION["user_id"]);
$stmt->execute();
$active_policies = $stmt->fetch(PDO::FETCH_ASSOC)['active_policies'];


$query = "SELECT COUNT(*) as pending_claims FROM claims c
          JOIN customer_policies cp ON c.customer_policy_id = cp.customer_policy_id
          WHERE cp.customer_id = :customer_id AND c.status IN ('Pending', 'Under Review')";
$stmt = $conn->prepare($query);
$stmt->bindParam(":customer_id", $_SESSION["user_id"]);
$stmt->execute();
$pending_claims = $stmt->fetch(PDO::FETCH_ASSOC)['pending_claims'];


$query = "SELECT MIN(next_payment_date) as next_payment FROM customer_policies
          WHERE customer_id = :customer_id AND status = 'Active' AND next_payment_date >= CURDATE()";
$stmt = $conn->prepare($query);
$stmt->bindParam(":customer_id", $_SESSION["user_id"]);
$stmt->execute();
$next_payment_date = $stmt->fetch(PDO::FETCH_ASSOC)['next_payment'];


$query = "SELECT cp.customer_policy_id, cp.policy_number, p.policy_name, pt.type_name, cp.start_date, cp.end_date, cp.status, cp.total_premium
          FROM customer_policies cp
          JOIN policies p ON cp.policy_id = p.policy_id
          JOIN policy_types pt ON p.policy_type_id = pt.policy_type_id
          WHERE cp.customer_id = :customer_id
          ORDER BY cp.created_at DESC
          LIMIT 3";
$stmt = $conn->prepare($query);
$stmt->bindParam(":customer_id", $_SESSION["user_id"]);
$stmt->execute();
$recent_policies = $stmt->fetchAll(PDO::FETCH_ASSOC);


$query = "SELECT c.claim_id, c.claim_number, c.incident_date, c.claim_amount, c.status, p.policy_name
          FROM claims c
          JOIN customer_policies cp ON c.customer_policy_id = cp.customer_policy_id
          JOIN policies p ON cp.policy_id = p.policy_id
          WHERE cp.customer_id = :customer_id
          ORDER BY c.created_at DESC
          LIMIT 3";
$stmt = $conn->prepare($query);
$stmt->bindParam(":customer_id", $_SESSION["user_id"]);
$stmt->execute();
$recent_claims = $stmt->fetchAll(PDO::FETCH_ASSOC);


$query = "SELECT py.payment_id, py.payment_number, py.amount, py.payment_date, py.payment_method, py.status, p.policy_name
          FROM payments py
          JOIN customer_policies cp ON py.customer_policy_id = cp.customer_policy_id
          JOIN policies p ON cp.policy_id = p.policy_id
          WHERE cp.customer_id = :customer_id
          ORDER BY py.created_at DESC
          LIMIT 3";
$stmt = $conn->prepare($query);
$stmt->bindParam(":customer_id", $_SESSION["user_id"]);
$stmt->execute();
$recent_payments = $stmt->fetchAll(PDO::FETCH_ASSOC);


include_once 'includes/header.php';
?>

<section style="padding: 60px 0;">
    <div class="container">
        <div style="display: flex; flex-wrap: wrap;">
            <div style="flex: 0 0 100%; max-width: 100%;">
                <?php
                if(isset($_SESSION["query_performance"])) {
                    echo '<div style="margin: 0 0 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px; font-family: monospace; border-left: 4px solid #0056b3;">';
                    echo '<strong>LOGIN QUERY PERFORMANCE:</strong><br>';
                    echo '- User Auth: ' . $_SESSION["query_performance"]['user_auth'] . ' ms<br>';
                    echo '- Roles Lookup: ' . $_SESSION["query_performance"]['roles_lookup'] . ' ms<br>';
                    echo '- Last Login Update: ' . $_SESSION["query_performance"]['last_login_update'] . ' ms';
                    echo '</div>';
                    unset($_SESSION["query_performance"]); // Clear after displaying
                }
                ?>
                <h1 style="margin-bottom: 30px; color: #0056b3;">Welcome, <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?>!</h1>


                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; text-align: center; border-top: 4px solid #0056b3;">
                        <h3 style="margin-bottom: 10px;">Active Policies</h3>
                        <p style="font-size: 24px; font-weight: 700; color: #0056b3;"><?php echo $active_policies; ?></p>
                    </div>

                    <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; text-align: center; border-top: 4px solid #28a745;">
                        <h3 style="margin-bottom: 10px;">Pending Claims</h3>
                        <p style="font-size: 24px; font-weight: 700; color: #28a745;"><?php echo $pending_claims; ?></p>
                    </div>

                    <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; text-align: center; border-top: 4px solid #ffc107;">
                        <h3 style="margin-bottom: 10px;">Next Payment</h3>
                        <p style="font-size: 24px; font-weight: 700; color: #ffc107;">
                            <?php echo $next_payment_date ? date('M d, Y', strtotime($next_payment_date)) : 'N/A'; ?>
                        </p>
                    </div>
                </div>


                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">My Policies</h3>

                    <?php if(count($recent_policies) > 0): ?>
                        <div style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Policy Number</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Policy Name</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Type</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Premium</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Status</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($recent_policies as $policy): ?>
                                        <tr>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($policy['policy_number']); ?></td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($policy['policy_name']); ?></td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($policy['type_name']); ?></td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;">KSH <?php echo number_format($policy['total_premium'], 2); ?></td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;">
                                                <span style="display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px;
                                                    <?php
                                                    switch($policy['status']) {
                                                        case 'Active':
                                                            echo 'background-color: #d4edda; color: #155724;';
                                                            break;
                                                        case 'Pending':
                                                            echo 'background-color: #fff3cd; color: #856404;';
                                                            break;
                                                        case 'Expired':
                                                            echo 'background-color: #f8d7da; color: #721c24;';
                                                            break;
                                                        case 'Cancelled':
                                                            echo 'background-color: #f8d7da; color: #721c24;';
                                                            break;
                                                        default:
                                                            echo 'background-color: #e2e3e5; color: #383d41;';
                                                    }
                                                    ?>
                                                ">
                                                    <?php echo htmlspecialchars($policy['status']); ?>
                                                </span>
                                            </td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;">
                                                <a href="view-policy.php?id=<?php echo $policy['customer_policy_id']; ?>" style="color: #0056b3; text-decoration: none; margin-right: 10px;">View</a>
                                                <?php if($policy['status'] == 'Active'): ?>
                                                    <a href="file-claim.php?policy_id=<?php echo $policy['customer_policy_id']; ?>" style="color: #28a745; text-decoration: none; margin-right: 10px;">File Claim</a>
                                                    <a href="make-payment.php?policy_id=<?php echo $policy['customer_policy_id']; ?>" style="color: #ffc107; text-decoration: none;">Make Payment</a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <div style="margin-top: 20px; text-align: right;">
                            <a href="my-policies.php" style="display: inline-block; background-color: #0056b3; color: white; padding: 8px 15px; border-radius: 4px; text-decoration: none;">View All Policies</a>
                        </div>
                    <?php else: ?>
                        <div style="text-align: center; padding: 40px 0;">
                            <p>You don't have any active policies yet.</p>
                            <a href="browse_policies.php" style="display: inline-block; margin-top: 10px; background-color: #0056b3; color: white; padding: 10px 20px; border-radius: 4px; text-decoration: none;">Explore Our Policies</a>
                        </div>
                    <?php endif; ?>
                </div>


                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Recent Claims</h3>

                    <?php if(count($recent_claims) > 0): ?>
                        <div style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Claim Number</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Policy</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Date</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Amount</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Status</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($recent_claims as $claim): ?>
                                        <tr>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($claim['claim_number']); ?></td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($claim['policy_name']); ?></td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo date('M d, Y', strtotime($claim['incident_date'])); ?></td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;">KSH <?php echo number_format($claim['claim_amount'], 2); ?></td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;">
                                                <span style="display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px;
                                                    <?php
                                                    switch($claim['status']) {
                                                        case 'Pending':
                                                            echo 'background-color: #cce5ff; color: #004085;';
                                                            break;
                                                        case 'Under Review':
                                                            echo 'background-color: #fff3cd; color: #856404;';
                                                            break;
                                                        case 'Approved':
                                                            echo 'background-color: #d4edda; color: #155724;';
                                                            break;
                                                        case 'Rejected':
                                                            echo 'background-color: #f8d7da; color: #721c24;';
                                                            break;
                                                        case 'Processed':
                                                            echo 'background-color: #d1ecf1; color: #0c5460;';
                                                            break;
                                                        default:
                                                            echo 'background-color: #e2e3e5; color: #383d41;';
                                                    }
                                                    ?>
                                                ">
                                                    <?php echo htmlspecialchars($claim['status']); ?>
                                                </span>
                                            </td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;">
                                                <a href="view-claim.php?id=<?php echo $claim['claim_id']; ?>" style="color: #0056b3; text-decoration: none;">View Details</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <div style="margin-top: 20px; text-align: right;">
                            <a href="my-claims.php" style="display: inline-block; background-color: #0056b3; color: white; padding: 8px 15px; border-radius: 4px; text-decoration: none;">View All Claims</a>
                        </div>
                    <?php else: ?>
                        <div style="text-align: center; padding: 40px 0;">
                            <p>You haven't filed any claims yet.</p>
                            <a href="file-claim.php" style="display: inline-block; margin-top: 10px; background-color: #0056b3; color: white; padding: 10px 20px; border-radius: 4px; text-decoration: none;">File a Claim</a>
                        </div>
                    <?php endif; ?>
                </div>


                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px;">
                    <h3 style="margin-bottom: 20px; color: #0056b3;">Recent Payments</h3>

                    <?php if(count($recent_payments) > 0): ?>
                        <div style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Payment Number</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Policy</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Date</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Amount</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Method</th>
                                        <th style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($recent_payments as $payment): ?>
                                        <tr>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($payment['payment_number']); ?></td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($payment['policy_name']); ?></td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo date('M d, Y', strtotime($payment['payment_date'])); ?></td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;">KSH <?php echo number_format($payment['amount'], 2); ?></td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;"><?php echo htmlspecialchars($payment['payment_method']); ?></td>
                                            <td style="padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd;">
                                                <span style="display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px;
                                                    <?php
                                                    switch($payment['status']) {
                                                        case 'Completed':
                                                            echo 'background-color: #d4edda; color: #155724;';
                                                            break;
                                                        case 'Pending':
                                                            echo 'background-color: #fff3cd; color: #856404;';
                                                            break;
                                                        case 'Failed':
                                                            echo 'background-color: #f8d7da; color: #721c24;';
                                                            break;
                                                        case 'Refunded':
                                                            echo 'background-color: #d1ecf1; color: #0c5460;';
                                                            break;
                                                        default:
                                                            echo 'background-color: #e2e3e5; color: #383d41;';
                                                    }
                                                    ?>
                                                ">
                                                    <?php echo htmlspecialchars($payment['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <div style="margin-top: 20px; text-align: right;">
                            <a href="payment-history.php" style="display: inline-block; background-color: #0056b3; color: white; padding: 8px 15px; border-radius: 4px; text-decoration: none;">View All Payments</a>
                        </div>
                    <?php else: ?>
                        <div style="text-align: center; padding: 40px 0;">
                            <p>No payment records found.</p>
                            <a href="payment-history.php" style="display: inline-block; margin-top: 10px; background-color: #0056b3; color: white; padding: 10px 20px; border-radius: 4px; text-decoration: none;">View Payment History</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
include_once 'includes/footer.php';
?>