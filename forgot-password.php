<?php
session_start();

// If user is already logged in, redirect to dashboard
if(isset($_SESSION["loggedin"]) && $_SESSION["loggedin"] === true) {
    switch($_SESSION["primary_role"]) {
        case "Administrator":
            header("location: admin/dashboard.php");
            break;
        case "Insurance Agent":
            header("location: agent/dashboard.php");
            break;
        case "System Accountant":
            header("location: accountant/dashboard.php");
            break;
        case "Customer":
        default:
            header("location: dashboard.php");
            break;
    }
    exit;
}

require_once "includes/db_config.php";

$username = $new_password = $confirm_password = "";
$username_err = $new_password_err = $confirm_password_err = $success_msg = $error_msg = "";
$user_found = false;
$user_name = "";

if($_SERVER["REQUEST_METHOD"] == "POST") {

    // Validate username/email first
    if(empty(trim($_POST["username"]))) {
        $username_err = "Please enter your username or email.";
    } else {
        $username = trim($_POST["username"]);

        // Check if user exists
        $sql = "SELECT user_id, username, email, first_name, last_name FROM users WHERE username = :username OR email = :email";
        if($stmt = $conn->prepare($sql)) {
            $stmt->bindParam(":username", $username, PDO::PARAM_STR);
            $stmt->bindParam(":email", $username, PDO::PARAM_STR);

            if($stmt->execute()) {
                if($stmt->rowCount() == 1) {
                    $user = $stmt->fetch();
                    $user_found = true;
                    $user_name = $user["first_name"] . " " . $user["last_name"];

                    // If password fields are filled, process password reset
                    if(!empty($_POST["new_password"]) || !empty($_POST["confirm_password"])) {

                        // Validate new password
                        if(empty(trim($_POST["new_password"]))) {
                            $new_password_err = "Please enter a new password.";
                        } elseif(strlen(trim($_POST["new_password"])) < 6) {
                            $new_password_err = "Password must have at least 6 characters.";
                        } else {
                            $new_password = trim($_POST["new_password"]);
                        }

                        // Validate confirm password
                        if(empty(trim($_POST["confirm_password"]))) {
                            $confirm_password_err = "Please confirm your password.";
                        } else {
                            $confirm_password = trim($_POST["confirm_password"]);
                            if(empty($new_password_err) && ($new_password != $confirm_password)) {
                                $confirm_password_err = "Password did not match.";
                            }
                        }

                        // Update password if no errors
                        if(empty($new_password_err) && empty($confirm_password_err)) {
                            $sql = "UPDATE users SET password = :password WHERE user_id = :user_id";

                            if($stmt = $conn->prepare($sql)) {
                                $param_password = password_hash($new_password, PASSWORD_DEFAULT);
                                $stmt->bindParam(":password", $param_password, PDO::PARAM_STR);
                                $stmt->bindParam(":user_id", $user["user_id"], PDO::PARAM_INT);

                                if($stmt->execute()) {
                                    $success_msg = "Password has been reset successfully! You can now login with your new password.";
                                    $username = $new_password = $confirm_password = "";
                                    $user_found = false;
                                } else {
                                    $error_msg = "Oops! Something went wrong. Please try again later.";
                                }
                                unset($stmt);
                            }
                        }
                    }
                } else {
                    $username_err = "No account found with that username or email.";
                }
            } else {
                $error_msg = "Oops! Something went wrong. Please try again later.";
            }
            unset($stmt);
        }
    }
}

$page_title = "Reset Password";
$page_description = "Reset your Zamara Insurance account password.";

include_once 'includes/header.php';
?>

<section style="padding: 80px 0;">
    <div class="container">
        <div style="max-width: 500px; margin: 0 auto; background-color: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);">

            <h2 style="text-align: center; margin-bottom: 30px; color: #0056b3;">Reset Your Password</h2>

            <?php if(!empty($success_msg)): ?>
                <div style="padding: 15px; margin-bottom: 20px; border-radius: 4px; background-color: #d4edda; color: #155724; text-align: center;">
                    <?php echo $success_msg; ?>
                </div>
                <div style="text-align: center;">
                    <a href="login.php" style="display: inline-block; background-color: #0056b3; color: white; padding: 12px 30px; border-radius: 4px; text-decoration: none; font-weight: 500;">Go to Login</a>
                </div>
            <?php else: ?>

                <?php if(!empty($error_msg)): ?>
                    <div style="padding: 15px; margin-bottom: 20px; border-radius: 4px; background-color: #f8d7da; color: #721c24;"><?php echo $error_msg; ?></div>
                <?php endif; ?>

                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" id="resetForm">
                    <div style="margin-bottom: 20px;">
                        <label for="username" style="display: block; margin-bottom: 5px; font-weight: 500;">Username or Email</label>
                        <input type="text" id="username" name="username" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" value="<?php echo $username; ?>" onblur="checkUser()">
                        <span style="color: #dc3545; font-size: 14px;"><?php echo $username_err; ?></span>
                    </div>

                    <?php if($user_found): ?>
                        <div style="padding: 10px; margin-bottom: 20px; border-radius: 4px; background-color: #d4edda; color: #155724; font-size: 14px;">
                            ✓ Account found: <strong><?php echo htmlspecialchars($user_name); ?></strong>
                        </div>
                    <?php endif; ?>

                    <div id="passwordFields" style="<?php echo $user_found ? 'display: block;' : 'display: none;'; ?>">
                        <div style="margin-bottom: 20px;">
                            <label for="new_password" style="display: block; margin-bottom: 5px; font-weight: 500;">New Password</label>
                            <input type="password" id="new_password" name="new_password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" value="<?php echo $new_password; ?>">
                            <span style="color: #dc3545; font-size: 14px;"><?php echo $new_password_err; ?></span>
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label for="confirm_password" style="display: block; margin-bottom: 5px; font-weight: 500;">Confirm New Password</label>
                            <input type="password" id="confirm_password" name="confirm_password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" value="<?php echo $confirm_password; ?>">
                            <span style="color: #dc3545; font-size: 14px;"><?php echo $confirm_password_err; ?></span>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <button type="submit" style="width: 100%; padding: 12px; background-color: <?php echo $user_found ? '#28a745' : '#0056b3'; ?>; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; font-weight: 500;">
                            <?php echo $user_found ? 'Reset Password' : 'Find Account'; ?>
                        </button>
                    </div>
                    <p style="text-align: center;"><a href="login.php" style="color: #0056b3; text-decoration: none;">← Back to Login</a></p>
                </form>

            <?php endif; ?>

        </div>
    </div>
</section>

<script>
function checkUser() {
    // This function can be enhanced with AJAX if needed
    // For now, the form submission handles user validation
}
</script>

<?php
include_once 'includes/footer.php';
?>
