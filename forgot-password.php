<?php
session_start();

// If user is already logged in, redirect to dashboard
if(isset($_SESSION["loggedin"]) && $_SESSION["loggedin"] === true) {
    switch($_SESSION["primary_role"]) {
        case "Administrator":
            header("location: admin/dashboard.php");
            break;
        case "Insurance Agent":
            header("location: agent/dashboard.php");
            break;
        case "System Accountant":
            header("location: accountant/dashboard.php");
            break;
        case "Customer":
        default:
            header("location: dashboard.php");
            break;
    }
    exit;
}

require_once "includes/db_config.php";

$username = $new_password = $confirm_password = "";
$username_err = $new_password_err = $confirm_password_err = $success_msg = $error_msg = "";
$step = 1; // Step 1: Enter username, Step 2: Reset password

if($_SERVER["REQUEST_METHOD"] == "POST") {
    
    if(isset($_POST["step"]) && $_POST["step"] == "1") {
        // Step 1: Validate username/email
        if(empty(trim($_POST["username"]))) {
            $username_err = "Please enter your username or email.";
        } else {
            $username = trim($_POST["username"]);
            
            // Check if user exists
            $sql = "SELECT user_id, username, email, first_name, last_name FROM users WHERE username = :username OR email = :email";
            if($stmt = $conn->prepare($sql)) {
                $stmt->bindParam(":username", $username, PDO::PARAM_STR);
                $stmt->bindParam(":email", $username, PDO::PARAM_STR);
                
                if($stmt->execute()) {
                    if($stmt->rowCount() == 1) {
                        $user = $stmt->fetch();
                        $_SESSION["reset_user_id"] = $user["user_id"];
                        $_SESSION["reset_username"] = $user["username"];
                        $_SESSION["reset_email"] = $user["email"];
                        $_SESSION["reset_name"] = $user["first_name"] . " " . $user["last_name"];
                        $step = 2;
                    } else {
                        $username_err = "No account found with that username or email.";
                    }
                } else {
                    $error_msg = "Oops! Something went wrong. Please try again later.";
                }
                unset($stmt);
            }
        }
    }
    
    if(isset($_POST["step"]) && $_POST["step"] == "2") {
        // Step 2: Reset password
        if(!isset($_SESSION["reset_user_id"])) {
            header("location: forgot-password.php");
            exit;
        }
        
        // Validate new password
        if(empty(trim($_POST["new_password"]))) {
            $new_password_err = "Please enter a new password.";
        } elseif(strlen(trim($_POST["new_password"])) < 6) {
            $new_password_err = "Password must have at least 6 characters.";
        } else {
            $new_password = trim($_POST["new_password"]);
        }
        
        // Validate confirm password
        if(empty(trim($_POST["confirm_password"]))) {
            $confirm_password_err = "Please confirm your password.";
        } else {
            $confirm_password = trim($_POST["confirm_password"]);
            if(empty($new_password_err) && ($new_password != $confirm_password)) {
                $confirm_password_err = "Password did not match.";
            }
        }
        
        // Update password if no errors
        if(empty($new_password_err) && empty($confirm_password_err)) {
            $sql = "UPDATE users SET password = :password WHERE user_id = :user_id";
            
            if($stmt = $conn->prepare($sql)) {
                $param_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt->bindParam(":password", $param_password, PDO::PARAM_STR);
                $stmt->bindParam(":user_id", $_SESSION["reset_user_id"], PDO::PARAM_INT);
                
                if($stmt->execute()) {
                    // Clear reset session data
                    unset($_SESSION["reset_user_id"]);
                    unset($_SESSION["reset_username"]);
                    unset($_SESSION["reset_email"]);
                    unset($_SESSION["reset_name"]);
                    
                    $success_msg = "Password has been reset successfully! You can now login with your new password.";
                    $step = 3; // Success step
                } else {
                    $error_msg = "Oops! Something went wrong. Please try again later.";
                }
                unset($stmt);
            }
        } else {
            $step = 2;
        }
    }
}

// Check if we're in step 2 from session
if(isset($_SESSION["reset_user_id"]) && $step == 1) {
    $step = 2;
}

$page_title = "Reset Password";
$page_description = "Reset your Zamara Insurance account password.";

include_once 'includes/header.php';
?>

<section style="padding: 80px 0;">
    <div class="container">
        <div style="max-width: 500px; margin: 0 auto; background-color: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);">
            
            <?php if($step == 1): ?>
                <h2 style="text-align: center; margin-bottom: 30px; color: #0056b3;">Reset Your Password</h2>
                <p style="text-align: center; margin-bottom: 30px; color: #666;">Enter your username or email address to reset your password.</p>
                
                <?php if(!empty($error_msg)): ?>
                    <div style="padding: 15px; margin-bottom: 20px; border-radius: 4px; background-color: #f8d7da; color: #721c24;"><?php echo $error_msg; ?></div>
                <?php endif; ?>
                
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                    <input type="hidden" name="step" value="1">
                    <div style="margin-bottom: 20px;">
                        <label for="username" style="display: block; margin-bottom: 5px; font-weight: 500;">Username or Email</label>
                        <input type="text" id="username" name="username" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" value="<?php echo $username; ?>">
                        <span style="color: #dc3545; font-size: 14px;"><?php echo $username_err; ?></span>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <button type="submit" style="width: 100%; padding: 12px; background-color: #0056b3; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; font-weight: 500;">Continue</button>
                    </div>
                    <p style="text-align: center;"><a href="login.php" style="color: #0056b3; text-decoration: none;">← Back to Login</a></p>
                </form>
                
            <?php elseif($step == 2): ?>
                <h2 style="text-align: center; margin-bottom: 20px; color: #0056b3;">Set New Password</h2>
                <p style="text-align: center; margin-bottom: 30px; color: #666;">
                    Hello <strong><?php echo htmlspecialchars($_SESSION["reset_name"]); ?></strong><br>
                    Please enter your new password below.
                </p>
                
                <?php if(!empty($error_msg)): ?>
                    <div style="padding: 15px; margin-bottom: 20px; border-radius: 4px; background-color: #f8d7da; color: #721c24;"><?php echo $error_msg; ?></div>
                <?php endif; ?>
                
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                    <input type="hidden" name="step" value="2">
                    <div style="margin-bottom: 20px;">
                        <label for="new_password" style="display: block; margin-bottom: 5px; font-weight: 500;">New Password</label>
                        <input type="password" id="new_password" name="new_password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;">
                        <span style="color: #dc3545; font-size: 14px;"><?php echo $new_password_err; ?></span>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <label for="confirm_password" style="display: block; margin-bottom: 5px; font-weight: 500;">Confirm New Password</label>
                        <input type="password" id="confirm_password" name="confirm_password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;">
                        <span style="color: #dc3545; font-size: 14px;"><?php echo $confirm_password_err; ?></span>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <button type="submit" style="width: 100%; padding: 12px; background-color: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; font-weight: 500;">Reset Password</button>
                    </div>
                    <p style="text-align: center;"><a href="forgot-password.php" style="color: #0056b3; text-decoration: none;">← Start Over</a></p>
                </form>
                
            <?php elseif($step == 3): ?>
                <h2 style="text-align: center; margin-bottom: 20px; color: #28a745;">Password Reset Successful!</h2>
                <div style="padding: 15px; margin-bottom: 20px; border-radius: 4px; background-color: #d4edda; color: #155724; text-align: center;">
                    <?php echo $success_msg; ?>
                </div>
                <div style="text-align: center;">
                    <a href="login.php" style="display: inline-block; background-color: #0056b3; color: white; padding: 12px 30px; border-radius: 4px; text-decoration: none; font-weight: 500;">Go to Login</a>
                </div>
            <?php endif; ?>
            
        </div>
    </div>
</section>

<?php
include_once 'includes/footer.php';
?>
