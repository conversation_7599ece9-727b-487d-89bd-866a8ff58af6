<?php
session_start();

if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: ../login.php");
    exit;
}

$is_admin = false;
foreach($_SESSION["roles"] as $role) {
    if($role['role_name'] == "Administrator") {
        $is_admin = true;
        break;
    }
}

if(!$is_admin) {
    header("location: ../dashboard.php");
    exit;
}

require_once '../includes/db_config.php';

$success_message = "";
$error_message = "";

// Handle view and edit modes
$view_mode = isset($_GET['view']) ? 'view' : (isset($_GET['edit']) ? 'edit' : 'list');
$selected_user_id = isset($_GET['view']) ? $_GET['view'] : (isset($_GET['edit']) ? $_GET['edit'] : null);
$selected_user = null;

if($selected_user_id) {
    // Get user details for view/edit
    $user_query = "SELECT u.*, GROUP_CONCAT(r.role_name SEPARATOR ', ') as roles
                   FROM users u
                   LEFT JOIN user_roles ur ON u.user_id = ur.user_id
                   LEFT JOIN roles r ON ur.role_id = r.role_id
                   WHERE u.user_id = :user_id
                   GROUP BY u.user_id";
    $user_stmt = $conn->prepare($user_query);
    $user_stmt->bindParam(':user_id', $selected_user_id);
    $user_stmt->execute();
    $selected_user = $user_stmt->fetch(PDO::FETCH_ASSOC);

    if(!$selected_user) {
        $error_message = "User not found.";
        $view_mode = 'list';
    }
}

if($_SERVER["REQUEST_METHOD"] == "POST") {
    if(isset($_POST['action'])) {
        switch($_POST['action']) {
            case 'create_user':
                $username = trim($_POST['username']);
                $email = trim($_POST['email']);
                $password = password_hash(trim($_POST['password']), PASSWORD_DEFAULT);
                $first_name = trim($_POST['first_name']);
                $last_name = trim($_POST['last_name']);
                $phone = trim($_POST['phone']);
                $role_id = $_POST['role_id'];

                try {
                    $conn->beginTransaction();

                    $query = "INSERT INTO users (username, email, password, first_name, last_name, phone) VALUES (?, ?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($query);
                    $stmt->execute([$username, $email, $password, $first_name, $last_name, $phone]);
                    $user_id = $conn->lastInsertId();

                    $query = "INSERT INTO user_roles (user_id, role_id, assigned_by) VALUES (?, ?, ?)";
                    $stmt = $conn->prepare($query);
                    $stmt->execute([$user_id, $role_id, $_SESSION['user_id']]);

                    $conn->commit();
                    $success_message = "User created successfully!";
                } catch(Exception $e) {
                    $conn->rollBack();
                    $error_message = "Error creating user: " . $e->getMessage();
                }
                break;

            case 'update_status':
                $user_id = $_POST['user_id'];
                $status = $_POST['status'];

                $query = "UPDATE users SET status = ? WHERE user_id = ?";
                $stmt = $conn->prepare($query);
                if($stmt->execute([$status, $user_id])) {
                    $success_message = "User status updated successfully!";
                } else {
                    $error_message = "Error updating user status.";
                }
                break;

            case 'delete_user':
                $user_id = $_POST['user_id'];

                if($user_id == $_SESSION['user_id']) {
                    $error_message = "You cannot delete your own account.";
                } else {
                    $query = "DELETE FROM users WHERE user_id = ?";
                    $stmt = $conn->prepare($query);
                    if($stmt->execute([$user_id])) {
                        $success_message = "User deleted successfully!";
                    } else {
                        $error_message = "Error deleting user.";
                    }
                }
                break;

            case 'update_user':
                $user_id = $_POST['user_id'];
                $username = trim($_POST['username']);
                $email = trim($_POST['email']);
                $first_name = trim($_POST['first_name']);
                $last_name = trim($_POST['last_name']);
                $phone = trim($_POST['phone']);
                $address = trim($_POST['address']);
                $status = $_POST['status'];

                try {
                    $update_sql = "UPDATE users SET username = :username, email = :email, first_name = :first_name,
                                   last_name = :last_name, phone = :phone, address = :address, status = :status,
                                   updated_at = NOW() WHERE user_id = :user_id";
                    $update_stmt = $conn->prepare($update_sql);
                    $update_stmt->bindParam(':username', $username);
                    $update_stmt->bindParam(':email', $email);
                    $update_stmt->bindParam(':first_name', $first_name);
                    $update_stmt->bindParam(':last_name', $last_name);
                    $update_stmt->bindParam(':phone', $phone);
                    $update_stmt->bindParam(':address', $address);
                    $update_stmt->bindParam(':status', $status);
                    $update_stmt->bindParam(':user_id', $user_id);

                    if($update_stmt->execute()) {
                        $success_message = "User updated successfully!";
                        // Refresh the selected user data
                        $user_query = "SELECT u.*, GROUP_CONCAT(r.role_name SEPARATOR ', ') as roles
                                       FROM users u
                                       LEFT JOIN user_roles ur ON u.user_id = ur.user_id
                                       LEFT JOIN roles r ON ur.role_id = r.role_id
                                       WHERE u.user_id = :user_id
                                       GROUP BY u.user_id";
                        $user_stmt = $conn->prepare($user_query);
                        $user_stmt->bindParam(':user_id', $user_id);
                        $user_stmt->execute();
                        $selected_user = $user_stmt->fetch(PDO::FETCH_ASSOC);
                    } else {
                        $error_message = "Error updating user.";
                    }
                } catch(PDOException $e) {
                    $error_message = "Error updating user: " . $e->getMessage();
                }
                break;
        }
    }
}

$query = "SELECT u.*, GROUP_CONCAT(r.role_name) as roles
          FROM users u
          LEFT JOIN user_roles ur ON u.user_id = ur.user_id
          LEFT JOIN roles r ON ur.role_id = r.role_id
          GROUP BY u.user_id
          ORDER BY u.created_at DESC";
$users = $conn->query($query)->fetchAll(PDO::FETCH_ASSOC);

$roles_query = "SELECT * FROM roles ORDER BY role_name";
$roles = $conn->query($roles_query)->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Manage Users";
$page_description = "Create, edit, and manage system users.";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Zamara Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .admin-header { background: #343a40; color: white; padding: 15px 0; margin-bottom: 30px; }
        .admin-nav { display: flex; justify-content: space-between; align-items: center; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background: #f8f9fa; font-weight: 600; }
        .form-group { margin-bottom: 15px; }
        .form-control { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); }
        .modal-content { background: white; margin: 5% auto; padding: 20px; width: 80%; max-width: 500px; border-radius: 8px; }
        .close { float: right; font-size: 28px; font-weight: bold; cursor: pointer; }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="admin-container">
            <div class="admin-nav">
                <h2>Zamara Admin - User Management</h2>
                <div>
                    <span>Welcome, <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?></span>
                    <a href="dashboard.php" class="btn btn-secondary" style="margin-left: 15px;">Dashboard</a>
                    <a href="../logout.php" class="btn btn-danger" style="margin-left: 10px;">Logout</a>
                </div>
            </div>
        </div>
    </header>

    <div class="admin-container">
        <?php if($success_message): ?>
            <div class="alert alert-success"><?php echo $success_message; ?></div>
        <?php endif; ?>

        <?php if($error_message): ?>
            <div class="alert alert-danger"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <?php if($view_mode == 'view' && $selected_user): ?>
            <!-- View User Details -->
            <div style="margin-bottom: 20px;">
                <a href="manage_users.php" class="btn btn-secondary">← Back to Users List</a>
            </div>

            <div style="background-color: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                <h1>User Details</h1>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 20px;">
                    <div>
                        <h3>Personal Information</h3>
                        <p><strong>User ID:</strong> <?php echo $selected_user['user_id']; ?></p>
                        <p><strong>Username:</strong> <?php echo htmlspecialchars($selected_user['username']); ?></p>
                        <p><strong>Email:</strong> <?php echo htmlspecialchars($selected_user['email']); ?></p>
                        <p><strong>First Name:</strong> <?php echo htmlspecialchars($selected_user['first_name']); ?></p>
                        <p><strong>Last Name:</strong> <?php echo htmlspecialchars($selected_user['last_name']); ?></p>
                        <p><strong>Phone:</strong> <?php echo htmlspecialchars($selected_user['phone'] ?: 'Not provided'); ?></p>
                        <p><strong>Address:</strong> <?php echo htmlspecialchars($selected_user['address'] ?: 'Not provided'); ?></p>
                    </div>

                    <div>
                        <h3>Account Information</h3>
                        <p><strong>Status:</strong>
                            <span class="badge <?php echo $selected_user['status'] == 'Active' ? 'btn-success' : 'btn-warning'; ?>">
                                <?php echo $selected_user['status']; ?>
                            </span>
                        </p>
                        <p><strong>Roles:</strong> <?php echo htmlspecialchars($selected_user['roles'] ?: 'No roles assigned'); ?></p>
                        <p><strong>Date of Birth:</strong> <?php echo $selected_user['date_of_birth'] ? date('M d, Y', strtotime($selected_user['date_of_birth'])) : 'Not provided'; ?></p>
                        <p><strong>Gender:</strong> <?php echo htmlspecialchars($selected_user['gender'] ?: 'Not specified'); ?></p>
                        <p><strong>Created:</strong> <?php echo date('M d, Y H:i', strtotime($selected_user['created_at'])); ?></p>
                        <p><strong>Last Updated:</strong> <?php echo $selected_user['updated_at'] ? date('M d, Y H:i', strtotime($selected_user['updated_at'])) : 'Never'; ?></p>
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <a href="?edit=<?php echo $selected_user['user_id']; ?>" class="btn btn-warning">Edit User</a>
                    <a href="manage_users.php" class="btn btn-secondary">Back to List</a>
                </div>
            </div>

        <?php elseif($view_mode == 'edit' && $selected_user): ?>
            <!-- Edit User Form -->
            <div style="margin-bottom: 20px;">
                <a href="manage_users.php" class="btn btn-secondary">← Back to Users List</a>
            </div>

            <div style="background-color: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                <h1>Edit User: <?php echo htmlspecialchars($selected_user['first_name'] . ' ' . $selected_user['last_name']); ?></h1>

                <form method="post" style="margin-top: 20px;">
                    <input type="hidden" name="action" value="update_user">
                    <input type="hidden" name="user_id" value="<?php echo $selected_user['user_id']; ?>">

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div class="form-group">
                            <label for="edit_username">Username:</label>
                            <input type="text" id="edit_username" name="username" class="form-control" value="<?php echo htmlspecialchars($selected_user['username']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_email">Email:</label>
                            <input type="email" id="edit_email" name="email" class="form-control" value="<?php echo htmlspecialchars($selected_user['email']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_first_name">First Name:</label>
                            <input type="text" id="edit_first_name" name="first_name" class="form-control" value="<?php echo htmlspecialchars($selected_user['first_name']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_last_name">Last Name:</label>
                            <input type="text" id="edit_last_name" name="last_name" class="form-control" value="<?php echo htmlspecialchars($selected_user['last_name']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_phone">Phone:</label>
                            <input type="text" id="edit_phone" name="phone" class="form-control" value="<?php echo htmlspecialchars($selected_user['phone']); ?>">
                        </div>

                        <div class="form-group">
                            <label for="edit_status">Status:</label>
                            <select id="edit_status" name="status" class="form-control" required>
                                <option value="Active" <?php echo $selected_user['status'] == 'Active' ? 'selected' : ''; ?>>Active</option>
                                <option value="Inactive" <?php echo $selected_user['status'] == 'Inactive' ? 'selected' : ''; ?>>Inactive</option>
                                <option value="Suspended" <?php echo $selected_user['status'] == 'Suspended' ? 'selected' : ''; ?>>Suspended</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_address">Address:</label>
                        <textarea id="edit_address" name="address" class="form-control" rows="3"><?php echo htmlspecialchars($selected_user['address']); ?></textarea>
                    </div>

                    <div style="margin-top: 30px;">
                        <button type="submit" class="btn btn-primary">Update User</button>
                        <a href="?view=<?php echo $selected_user['user_id']; ?>" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>

        <?php else: ?>
            <!-- Users List -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h1>User Management</h1>
                <button onclick="openModal('createUserModal')" class="btn btn-primary">Create New User</button>
            </div>

            <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Username</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Roles</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach($users as $user): ?>
                <tr>
                    <td><?php echo $user['user_id']; ?></td>
                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                    <td><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></td>
                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                    <td><?php echo htmlspecialchars($user['roles'] ?: 'No roles'); ?></td>
                    <td>
                        <span class="badge <?php echo $user['status'] == 'Active' ? 'btn-success' : 'btn-warning'; ?>">
                            <?php echo $user['status']; ?>
                        </span>
                    </td>
                    <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                    <td>
                        <a href="?view=<?php echo $user['user_id']; ?>" class="btn btn-info btn-sm" style="margin-right: 5px;">View</a>
                        <a href="?edit=<?php echo $user['user_id']; ?>" class="btn btn-warning btn-sm" style="margin-right: 5px;">Edit</a>

                        <?php if($user['user_id'] != $_SESSION['user_id']): ?>
                            <form method="post" style="display: inline;">
                                <input type="hidden" name="action" value="update_status">
                                <input type="hidden" name="user_id" value="<?php echo $user['user_id']; ?>">
                                <select name="status" onchange="this.form.submit()" class="btn btn-sm" style="font-size: 12px;">
                                    <option value="Active" <?php echo $user['status'] == 'Active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="Inactive" <?php echo $user['status'] == 'Inactive' ? 'selected' : ''; ?>>Inactive</option>
                                    <option value="Suspended" <?php echo $user['status'] == 'Suspended' ? 'selected' : ''; ?>>Suspended</option>
                                </select>
                            </form>

                            <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user?')">
                                <input type="hidden" name="action" value="delete_user">
                                <input type="hidden" name="user_id" value="<?php echo $user['user_id']; ?>">
                                <button type="submit" class="btn btn-danger btn-sm" style="font-size: 12px;">Delete</button>
                            </form>
                        <?php else: ?>
                            <span class="text-muted">Current User</span>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php endif; ?>
    </div>

    <!-- Create User Modal -->
    <div id="createUserModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('createUserModal')">&times;</span>
            <h3>Create New User</h3>
            <form method="post">
                <input type="hidden" name="action" value="create_user">

                <div class="form-group">
                    <label>Username:</label>
                    <input type="text" name="username" class="form-control" required>
                </div>

                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" name="email" class="form-control" required>
                </div>

                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" name="password" class="form-control" required>
                </div>

                <div class="form-group">
                    <label>First Name:</label>
                    <input type="text" name="first_name" class="form-control" required>
                </div>

                <div class="form-group">
                    <label>Last Name:</label>
                    <input type="text" name="last_name" class="form-control" required>
                </div>

                <div class="form-group">
                    <label>Phone:</label>
                    <input type="text" name="phone" class="form-control">
                </div>

                <div class="form-group">
                    <label>Role:</label>
                    <select name="role_id" class="form-control" required>
                        <option value="">Select Role</option>
                        <?php foreach($roles as $role): ?>
                            <option value="<?php echo $role['role_id']; ?>"><?php echo htmlspecialchars($role['role_name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div style="text-align: right;">
                    <button type="button" onclick="closeModal('createUserModal')" class="btn btn-secondary">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create User</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
